import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/gnss_provider.dart';
import '../providers/bluetooth_provider.dart';
import '../providers/terminal_provider.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  Future<void> _applySettings() async {
    final gnss = context.read<GnssProvider>();
    final bluetooth = context.read<BluetoothProvider>();
    final terminal = context.read<TerminalProvider>();

    // Generate individual commands as strings
    final cutoffAngleCommand = gnss.generateCutoffAngleCommand();
    final multipathMitigationCommand = gnss.generateMultipathMitigationCommand();
    final antiJammingCommand = gnss.generateAntiJammingCommand();

    // Store commands in a list for sequential processing
    final commands = [
      cutoffAngleCommand,
      multipathMitigationCommand,
      antiJammingCommand,
    ];

    if (bluetooth.isConnected) {
      bool allSuccessful = true;

      // Send each command with 200ms delay between them
      for (int i = 0; i < commands.length; i++) {
        final command = commands[i];
        terminal.addCommandEcho(command);

        final success = await bluetooth.sendCommand(command);
        if (!success) {
          allSuccessful = false;
          terminal.addSystemMessage('Failed to send command: $command');
        } else {
          terminal.addSystemMessage('Command sent: $command');
        }

        // Add 200ms delay between commands (except after the last one)
        if (i < commands.length - 1) {
          await Future.delayed(const Duration(milliseconds: 200));
        }
      }

      if (allSuccessful) {
        terminal.addSystemMessage('All settings applied successfully');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Settings applied successfully')),
          );
        }
      } else {
        terminal.addSystemMessage('Some settings failed to apply');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Some settings failed to apply')),
          );
        }
      }
    } else {
      // Log commands that would be sent for debugging
      for (final command in commands) {
        terminal.addCommandEcho(command);
      }
      terminal.addSystemMessage('No device connected - settings saved locally');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Settings saved (no device connected)')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Consumer<GnssProvider>(
        builder: (context, gnss, child) {
          return SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Cutoff Angle Card
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            const Icon(Icons.visibility),
                            const SizedBox(width: 8),
                            Text(
                              'Cutoff Angle',
                              style: Theme.of(context).textTheme.titleMedium,
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        const Text(
                          'Minimum elevation angle for satellite tracking',
                          style: TextStyle(color: Colors.grey),
                        ),
                        const SizedBox(height: 16),
                        DropdownButtonFormField<int>(
                          value: gnss.gnssSettings.cutoffAngle,
                          decoration: const InputDecoration(
                            labelText: 'Cutoff Angle',
                            border: OutlineInputBorder(),
                            suffixText: '°',
                          ),
                          items: GnssProvider.cutoffAngleOptions.map((angle) {
                            return DropdownMenuItem(
                              value: angle,
                              child: Text('$angle°'),
                            );
                          }).toList(),
                          onChanged: (angle) {
                            if (angle != null) {
                              gnss.updateCutoffAngle(angle);
                            }
                          },
                        ),
                      ],
                    ),
                  ),
                ),

                const SizedBox(height: 16),

                // Multipath Mitigation Card
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            const Icon(Icons.route),
                            const SizedBox(width: 8),
                            Text(
                              'Multipath Mitigation',
                              style: Theme.of(context).textTheme.titleMedium,
                            ),
                            const Spacer(),
                            Switch(
                              value: gnss.gnssSettings.multipathMitigation,
                              onChanged: (value) {
                                gnss.updateMultipathMitigation(value);
                              },
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        const Text(
                          'Reduces errors caused by signal reflections from nearby objects',
                          style: TextStyle(color: Colors.grey),
                        ),
                      ],
                    ),
                  ),
                ),

                const SizedBox(height: 16),

                // Anti-Jamming Card
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            const Icon(Icons.security),
                            const SizedBox(width: 8),
                            Text(
                              'Anti-Jamming',
                              style: Theme.of(context).textTheme.titleMedium,
                            ),
                            const Spacer(),
                            Switch(
                              value: gnss.gnssSettings.antiJamming,
                              onChanged: (value) {
                                gnss.updateAntiJamming(value);
                              },
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        const Text(
                          'Protects against intentional signal interference',
                          style: TextStyle(color: Colors.grey),
                        ),
                      ],
                    ),
                  ),
                ),

                const SizedBox(height: 16),

                // Current Settings Summary Card
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            const Icon(Icons.info_outline),
                            const SizedBox(width: 8),
                            Text(
                              'Current Settings',
                              style: Theme.of(context).textTheme.titleMedium,
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        _buildSettingRow(
                          'Cutoff Angle',
                          '${gnss.gnssSettings.cutoffAngle}°',
                          Icons.visibility,
                        ),
                        const SizedBox(height: 8),
                        _buildSettingRow(
                          'Multipath Mitigation',
                          gnss.gnssSettings.multipathMitigation ? 'Enabled' : 'Disabled',
                          Icons.route,
                          valueColor: gnss.gnssSettings.multipathMitigation ? Colors.green : Colors.grey,
                        ),
                        const SizedBox(height: 8),
                        _buildSettingRow(
                          'Anti-Jamming',
                          gnss.gnssSettings.antiJamming ? 'Enabled' : 'Disabled',
                          Icons.security,
                          valueColor: gnss.gnssSettings.antiJamming ? Colors.green : Colors.grey,
                        ),
                      ],
                    ),
                  ),
                ),

                const SizedBox(height: 32),

                // Apply Button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    onPressed: _applySettings,
                    icon: const Icon(Icons.check),
                    label: const Text('Apply Settings'),
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildSettingRow(String label, String value, IconData icon, {Color? valueColor}) {
    return Row(
      children: [
        Icon(icon, size: 20, color: Colors.grey),
        const SizedBox(width: 8),
        Text(label),
        const Spacer(),
        Text(
          value,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: valueColor,
          ),
        ),
      ],
    );
  }
}
