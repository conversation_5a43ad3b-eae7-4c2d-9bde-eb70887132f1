import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/gnss_provider.dart';
import '../providers/bluetooth_provider.dart';
import '../providers/terminal_provider.dart';
import '../models/gnss_models.dart';

class GnssModeScreen extends StatefulWidget {
  const GnssModeScreen({super.key});

  @override
  State<GnssModeScreen> createState() => _GnssModeScreenState();
}

class _GnssModeScreenState extends State<GnssModeScreen> {
  final _minimumTimeController = TextEditingController();
  final _minimumAccuracyController = TextEditingController();
  final _latitudeController = TextEditingController();
  final _longitudeController = TextEditingController();
  final _elevationController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _initializeControllers();
  }

  void _initializeControllers() {
    final gnss = context.read<GnssProvider>();
    _minimumTimeController.text = gnss.gnssConfig.minimumTime.toString();
    _minimumAccuracyController.text = gnss.gnssConfig.minimumAccuracy.toString();
    _latitudeController.text = gnss.gnssConfig.latitude?.toString() ?? '';
    _longitudeController.text = gnss.gnssConfig.longitude?.toString() ?? '';
    _elevationController.text = gnss.gnssConfig.elevation?.toString() ?? '';
  }

  Future<void> _applyConfiguration() async {
    final gnss = context.read<GnssProvider>();
    final bluetooth = context.read<BluetoothProvider>();
    final terminal = context.read<TerminalProvider>();

    // Update configuration from text fields
    if (_minimumTimeController.text.isNotEmpty) {
      gnss.updateMinimumTime(int.tryParse(_minimumTimeController.text) ?? 60);
    }
    if (_minimumAccuracyController.text.isNotEmpty) {
      gnss.updateMinimumAccuracy(double.tryParse(_minimumAccuracyController.text) ?? 1.0);
    }
    if (gnss.gnssConfig.mode == GnssMode.knownBase) {
      gnss.updateKnownBasePosition(
        latitude: double.tryParse(_latitudeController.text),
        longitude: double.tryParse(_longitudeController.text),
        elevation: double.tryParse(_elevationController.text),
      );
    }

    // Generate complete command sequence for the selected mode
    final commands = gnss.generateGnssModeCommandSequence();

    if (bluetooth.isConnected) {
      bool allSuccessful = true;

      // Send each command with 200ms delay between them
      for (int i = 0; i < commands.length; i++) {
        final command = commands[i];
        terminal.addCommandEcho(command);

        final success = await bluetooth.sendCommand(command);
        if (!success) {
          allSuccessful = false;
          terminal.addSystemMessage('Failed: $command');
        } else {
          terminal.addSystemMessage(command);
        }

        // Add 200ms delay between commands (except after the last one)
        if (i < commands.length - 1) {
          await Future.delayed(const Duration(milliseconds: 200));
        }
      }

      if (allSuccessful) {
        terminal.addSystemMessage('GNSS mode configuration applied successfully');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Configuration applied successfully')),
          );
        }
      } else {
        terminal.addSystemMessage('Some configuration commands failed');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Some configuration commands failed')),
          );
        }
      }
    } else {
      // Log commands that would be sent for debugging
      for (final command in commands) {
        terminal.addCommandEcho(command);
      }
      terminal.addSystemMessage('No device connected - configuration saved locally');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Configuration saved (no device connected)')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Consumer<GnssProvider>(
        builder: (context, gnss, child) {
          return Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Mode Selection Card
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'GNSS Operation Mode',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        const SizedBox(height: 16),
                        DropdownButtonFormField<GnssMode>(
                          value: gnss.gnssConfig.mode,
                          decoration: const InputDecoration(
                            labelText: 'Mode',
                            border: OutlineInputBorder(),
                          ),
                          items: GnssMode.values.map((mode) {
                            return DropdownMenuItem(
                              value: mode,
                              child: Text(_getModeDisplayName(mode)),
                            );
                          }).toList(),
                          onChanged: (mode) {
                            if (mode != null) {
                              gnss.updateGnssMode(mode);
                            }
                          },
                        ),
                      ],
                    ),
                  ),
                ),

                const SizedBox(height: 16),

                // Mode-specific Configuration
                Expanded(
                  child: SingleChildScrollView(
                    child: _buildModeSpecificConfig(gnss),
                  ),
                ),

                const SizedBox(height: 16),

                // Apply Button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    onPressed: _applyConfiguration,
                    icon: const Icon(Icons.check),
                    label: const Text('Apply Configuration'),
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildModeSpecificConfig(GnssProvider gnss) {
    switch (gnss.gnssConfig.mode) {
      case GnssMode.ppp:
        return _buildPppConfig();
      case GnssMode.rover:
        return _buildRoverConfig(gnss);
      case GnssMode.averageBase:
        return _buildAverageBaseConfig(gnss);
      case GnssMode.knownBase:
        return _buildKnownBaseConfig(gnss);
    }
  }

  Widget _buildPppConfig() {
    return const Card(
      child: Padding(
        padding: EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Precise Point Positioning (PPP)'),
            SizedBox(height: 8),
            Text(
              'PPP mode uses satellite clock and orbit corrections to achieve high accuracy positioning without base station corrections.',
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRoverConfig(GnssProvider gnss) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Rover Configuration',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<int>(
              value: gnss.gnssConfig.frequency,
              decoration: const InputDecoration(
                labelText: 'Update Frequency',
                border: OutlineInputBorder(),
                suffixText: 'Hz',
              ),
              items: GnssProvider.frequencyOptions.map((freq) {
                return DropdownMenuItem(
                  value: freq,
                  child: Text('$freq Hz'),
                );
              }).toList(),
              onChanged: (freq) {
                if (freq != null) {
                  gnss.updateFrequency(freq);
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAverageBaseConfig(GnssProvider gnss) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Average Base Configuration',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _minimumTimeController,
              decoration: const InputDecoration(
                labelText: 'Minimum Time',
                border: OutlineInputBorder(),
                suffixText: 'seconds',
              ),
              keyboardType: TextInputType.number,
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _minimumAccuracyController,
              decoration: const InputDecoration(
                labelText: 'Minimum Accuracy',
                border: OutlineInputBorder(),
                suffixText: 'meters',
              ),
              keyboardType: TextInputType.number,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildKnownBaseConfig(GnssProvider gnss) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Known Base Configuration',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _latitudeController,
              decoration: const InputDecoration(
                labelText: 'Latitude',
                border: OutlineInputBorder(),
                suffixText: '°',
              ),
              keyboardType: TextInputType.number,
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _longitudeController,
              decoration: const InputDecoration(
                labelText: 'Longitude',
                border: OutlineInputBorder(),
                suffixText: '°',
              ),
              keyboardType: TextInputType.number,
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _elevationController,
              decoration: const InputDecoration(
                labelText: 'Elevation',
                border: OutlineInputBorder(),
                suffixText: 'm',
              ),
              keyboardType: TextInputType.number,
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _minimumAccuracyController,
              decoration: const InputDecoration(
                labelText: 'Minimum Accuracy',
                border: OutlineInputBorder(),
                suffixText: 'meters',
              ),
              keyboardType: TextInputType.number,
            ),
          ],
        ),
      ),
    );
  }

  String _getModeDisplayName(GnssMode mode) {
    switch (mode) {
      case GnssMode.ppp:
        return 'Single';
      case GnssMode.rover:
        return 'Rover';
      case GnssMode.averageBase:
        return 'Average Base';
      case GnssMode.knownBase:
        return 'Known Base';
    }
  }

  @override
  void dispose() {
    _minimumTimeController.dispose();
    _minimumAccuracyController.dispose();
    _latitudeController.dispose();
    _longitudeController.dispose();
    _elevationController.dispose();
    super.dispose();
  }
}
