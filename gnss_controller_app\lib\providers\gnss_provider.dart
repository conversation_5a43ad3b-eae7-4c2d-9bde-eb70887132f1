import 'dart:async';
import 'package:flutter/foundation.dart';
import '../models/gnss_models.dart';

class GnssProvider extends ChangeNotifier {
  GnssConfiguration _gnssConfig = GnssConfiguration(mode: GnssMode.rover);
  CorrectionConfiguration _correctionConfig = CorrectionConfiguration(
    autoRestart: false,
    mode: CorrectionMode.bluetooth,
    loraSettings: LoRaSettings(
      channel: 1,
      airDataRate: 9.6,
      transmissionPower: 20,
    ),
  );
  GnssSettings _gnssSettings = GnssSettings(
    cutoffAngle: 15,
    multipathMitigation: true,
    antiJamming: false,
  );
  CorrectionStatus _correctionStatus = CorrectionStatus(
    bluetoothActive: false,
    ntripActive: false,
    loraActive: false,
  );
  LoggingStatus _loggingStatus = LoggingStatus.stopped;
  List<LogEntry> _logEntries = [];
  Duration _loggingDuration = Duration.zero;
  Timer? _loggingTimer;
  bool _autoRestartLogging = false;

  // Getters
  GnssConfiguration get gnssConfig => _gnssConfig;
  CorrectionConfiguration get correctionConfig => _correctionConfig;
  GnssSettings get gnssSettings => _gnssSettings;
  CorrectionStatus get correctionStatus => _correctionStatus;
  LoggingStatus get loggingStatus => _loggingStatus;
  List<LogEntry> get logEntries => _logEntries;
  Duration get loggingDuration => _loggingDuration;
  bool get autoRestartLogging => _autoRestartLogging;

  // Available options
  static const List<int> frequencyOptions = [1, 5, 10];
  static const List<int> cutoffAngleOptions = [5, 10, 15, 20, 25, 30];
  static const List<double> airDataRateOptions = [1.2, 2.4, 4.8, 9.6, 19.2, 38.4];

  // Generate these lists dynamically since they can't be const
  static List<int> get loraChannelOptions => List.generate(15, (i) => i + 1);
  static List<int> get transmissionPowerOptions => List.generate(28, (i) => i + 10); // 10-37 dBm

  // GNSS Mode Configuration
  void updateGnssMode(GnssMode mode) {
    _gnssConfig = _gnssConfig.copyWith(mode: mode);
    notifyListeners();
  }

  void updateFrequency(int frequency) {
    _gnssConfig = _gnssConfig.copyWith(frequency: frequency);
    notifyListeners();
  }

  void updateMinimumTime(int time) {
    _gnssConfig = _gnssConfig.copyWith(minimumTime: time);
    notifyListeners();
  }

  void updateMinimumAccuracy(double accuracy) {
    _gnssConfig = _gnssConfig.copyWith(minimumAccuracy: accuracy);
    notifyListeners();
  }

  void updateKnownBasePosition({
    double? latitude,
    double? longitude,
    double? elevation,
  }) {
    _gnssConfig = _gnssConfig.copyWith(
      latitude: latitude,
      longitude: longitude,
      elevation: elevation,
    );
    notifyListeners();
  }

  String generateGnssModeCommand() {
    switch (_gnssConfig.mode) {
      case GnssMode.ppp:
        return 'MODE PPP';
      case GnssMode.rover:
        return 'MODE ROVER ${_gnssConfig.frequency}HZ';
      case GnssMode.averageBase:
        return 'MODE AVGBASE ${_gnssConfig.minimumTime} ${_gnssConfig.minimumAccuracy}';
      case GnssMode.knownBase:
        return 'MODE KNOWNBASE ${_gnssConfig.latitude} ${_gnssConfig.longitude} ${_gnssConfig.elevation} ${_gnssConfig.minimumAccuracy}';
    }
  }

  // Correction Configuration
  void updateCorrectionMode(CorrectionMode mode) {
    _correctionConfig = _correctionConfig.copyWith(mode: mode);
    notifyListeners();
  }

  void updateAutoRestart(bool autoRestart) {
    _correctionConfig = _correctionConfig.copyWith(autoRestart: autoRestart);
    notifyListeners();
  }

  void updateLoRaChannel(int channel) {
    final newLoraSettings = _correctionConfig.loraSettings.copyWith(channel: channel);
    _correctionConfig = _correctionConfig.copyWith(loraSettings: newLoraSettings);
    notifyListeners();
  }

  void updateLoRaAirDataRate(double rate) {
    final newLoraSettings = _correctionConfig.loraSettings.copyWith(airDataRate: rate);
    _correctionConfig = _correctionConfig.copyWith(loraSettings: newLoraSettings);
    notifyListeners();
  }

  void updateLoRaTransmissionPower(int power) {
    final newLoraSettings = _correctionConfig.loraSettings.copyWith(transmissionPower: power);
    _correctionConfig = _correctionConfig.copyWith(loraSettings: newLoraSettings);
    notifyListeners();
  }

  String generateCorrectionCommand() {
    switch (_correctionConfig.mode) {
      case CorrectionMode.bluetooth:
        return 'CORRECTION BLUETOOTH';
      case CorrectionMode.ntrip:
        return 'CORRECTION NTRIP';
      case CorrectionMode.lora:
        final lora = _correctionConfig.loraSettings;
        return 'CORRECTION LORA ${lora.channel} ${lora.airDataRate} ${lora.transmissionPower}';
    }
  }

  // GNSS Settings
  void updateCutoffAngle(int angle) {
    _gnssSettings = _gnssSettings.copyWith(cutoffAngle: angle);
    notifyListeners();
  }

  void updateMultipathMitigation(bool enabled) {
    _gnssSettings = _gnssSettings.copyWith(multipathMitigation: enabled);
    notifyListeners();
  }

  void updateAntiJamming(bool enabled) {
    _gnssSettings = _gnssSettings.copyWith(antiJamming: enabled);
    notifyListeners();
  }

  String generateSettingsCommand() {
    return 'SETTINGS ${_gnssSettings.cutoffAngle} ${_gnssSettings.multipathMitigation ? 'ON' : 'OFF'} ${_gnssSettings.antiJamming ? 'ON' : 'OFF'}';
  }

  // Individual setting commands for UM980 device
  String generateCutoffAngleCommand() {
    return 'MASK ${_gnssSettings.cutoffAngle}';
  }

  String generateMultipathMitigationCommand() {
    return _gnssSettings.multipathMitigation
        ? 'CONFIG MMP ENABLE'
        : 'CONFIG MMP DISABLE';
  }

  String generateAntiJammingCommand() {
    return _gnssSettings.antiJamming
        ? 'CONFIG ANTIJAM AUTO'
        : 'CONFIG ANTIJAM DISABLE';
  }

  // Logging Management
  void updateAutoRestartLogging(bool autoRestart) {
    _autoRestartLogging = autoRestart;
    notifyListeners();
  }

  void startLogging() {
    if (_loggingStatus == LoggingStatus.recording) return;

    _loggingStatus = LoggingStatus.recording;
    _loggingDuration = Duration.zero;

    _loggingTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      _loggingDuration = Duration(seconds: _loggingDuration.inSeconds + 1);
      notifyListeners();
    });

    notifyListeners();
  }

  void stopLogging() {
    if (_loggingStatus == LoggingStatus.stopped) return;

    _loggingTimer?.cancel();
    _loggingTimer = null;

    // Create a new log entry
    final logEntry = LogEntry(
      fileName: 'log_${DateTime.now().millisecondsSinceEpoch}.rinex',
      timestamp: DateTime.now(),
      sizeBytes: (_loggingDuration.inSeconds * 1024).round(), // Simulated size
      duration: _loggingDuration,
    );

    _logEntries.insert(0, logEntry);
    _loggingStatus = LoggingStatus.stopped;
    _loggingDuration = Duration.zero;

    notifyListeners();
  }

  void deleteLogEntry(LogEntry entry) {
    _logEntries.remove(entry);
    notifyListeners();
  }

  String generateLoggingCommand(bool start) {
    return start ? 'LOGGING START' : 'LOGGING STOP';
  }

  // Correction Status Updates (would be updated from device responses)
  void updateCorrectionStatus({
    bool? bluetoothActive,
    bool? ntripActive,
    bool? loraActive,
  }) {
    _correctionStatus = CorrectionStatus(
      bluetoothActive: bluetoothActive ?? _correctionStatus.bluetoothActive,
      ntripActive: ntripActive ?? _correctionStatus.ntripActive,
      loraActive: loraActive ?? _correctionStatus.loraActive,
    );
    notifyListeners();
  }

  @override
  void dispose() {
    _loggingTimer?.cancel();
    super.dispose();
  }
}
